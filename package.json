{"name": "@skywind-group/sw-currency-exchange", "version": "1.6.19", "description": "Currency exchange service", "license": "ISC", "author": "", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"compile": "gulp compile", "clean": "gulp clean", "only-test": "istanbul cover --print both node_modules/mocha/bin/_mocha -- -R spec --timeout 6000 lib/test/**/*.js", "test": "npm run only-test && npm run remap && npm run sonar", "sonar": "gulp sonar", "remap": "remap-istanbul -i ./coverage/coverage.raw.json -o ./coverage/lcov-final.lcov -t lcovonly -b src", "tslint": "gulp tslint"}, "devDependencies": {"@skywind-group/sw-utils": "1.0.18", "@types/chai": "4.0.1", "@types/chai-as-promised": "0.0.29", "@types/mocha": "2.2.40", "@types/node": "^14.18.63", "@types/sinon": "1.16.36", "bole": "3.0.2", "chai": "4.1.2", "chai-as-promised": "7.1.1", "fast-glob": "^3.3.2", "gulp": "4.0.2", "gulp-clean-compiled-typescript": "1.2.0", "gulp-mocha": "6.0.0", "gulp-sourcemaps": "2.6.1", "gulp-tslint": "8.1.4", "gulp-typescript": "3.1.6", "ioredis": "3.2.2", "ioredis-mock": "3.13.0", "istanbul": "1.1.0-alpha.1", "kafka-node": "4.0.1", "mocha": "3.2.0", "mocha-typescript": "1.1.12", "remap-istanbul": "0.12.0", "sequelize": "4.41.2", "sinon": "2.1.0", "sonarqube-scanner": "^2.1.1", "ts-node": "6.0.5", "tslint": "4.5.1", "typescript": "^4.2.4", "typings": "2.1.0"}, "dependencies": {"jsonwebtoken": "8.4.0", "node-schedule": "1.3.0", "reflect-metadata": "0.1.13", "request": "2.87.0"}}